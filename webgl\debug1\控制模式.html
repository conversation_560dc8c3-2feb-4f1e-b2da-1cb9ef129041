<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>控制模式 - 桂林智源 SVG 数字化系统</title>
    <link rel="shortcut icon" href="../logo.png">
    <!-- 引入 MQTT 客户端库 -->
    <script src="https://unpkg.com/mqtt@4.3.7/dist/mqtt.min.js"></script>
    <!-- 引入通用参数配置样式 -->
    <link rel="stylesheet" href="../common/parameter-config.css">
    <!-- 控制模式专用样式 -->
    <style>
        /* 控制模式专用样式扩展 */
        .control-mode-panel {
            flex: 1;
            background: rgba(26, 31, 46, 0.95);
            border: 2px solid rgba(0, 212, 255, 0.4);
            border-radius: 8px;
            padding: 15px;
            overflow: visible;
            min-width: 0;
            margin-bottom: 15px;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 10px; /* 减少间距 */
            position: relative;
            padding-bottom: 70px; /* 减少底部预留空间 */
            overflow: hidden; /* 防止内容溢出 */
            min-height: 0; /* 允许收缩 */
        }

        /* 控件容器样式 - 统一所有控件的容器 */
        .control-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            padding: 4px 0;
        }

        /* 整数输入框样式 */
        .integer-input {
            width: 80px;
            height: 30px;
            background: rgba(42, 49, 66, 0.9);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 4px;
            color: #ffffff;
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            margin: 0 auto;
            display: block;
        }

        .integer-input:focus {
            outline: none;
            border-color: rgba(0, 212, 255, 0.6);
            box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
        }

        .integer-input:invalid {
            border-color: rgba(220, 53, 69, 0.6);
            box-shadow: 0 0 5px rgba(220, 53, 69, 0.3);
        }

        /* 下拉选择框样式 */
        .select-dropdown {
            width: 100px;
            height: 30px;
            background: rgba(42, 49, 66, 0.9);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 4px;
            color: #ffffff;
            font-size: 11px;
            font-weight: bold;
            margin: 0 auto;
            cursor: pointer;
            display: block;
        }

        .select-dropdown:focus {
            outline: none;
            border-color: rgba(0, 212, 255, 0.6);
            box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
        }

        .select-dropdown option {
            background: rgba(42, 49, 66, 0.95);
            color: #ffffff;
            padding: 5px;
        }

        /* 开关控件容器样式 - 水平布局，开关在左，文本在右 */
        .switch-control-container {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            width: 100%;
            height: 100%;
            padding: 4px;
            gap: 8px; /* 开关和文本之间的间距 */
        }

        /* 重写开关样式以确保一致性 */
        .toggle-switch {
            position: relative;
            width: 60px !important;
            height: 30px !important;
            background: #dc3545;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 212, 255, 0.3);
            flex-shrink: 0; /* 防止收缩 */
            margin: 0; /* 移除margin，使用容器的gap控制间距 */
        }

        .toggle-switch.active {
            background: #007bff;
        }

        .toggle-switch::before {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            width: 24px;
            height: 24px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .toggle-switch.active::before {
            transform: translateX(30px);
        }

        /* 开关状态文本样式 - 放在开关右侧 */
        .switch-status-text {
            font-size: 11px;
            color: #7a8ba0;
            text-align: left;
            width: 60px; /* 固定宽度，防止影响开关位置 */
            height: 30px; /* 与开关同高 */
            line-height: 30px; /* 垂直居中 */
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin: 0;
            flex-shrink: 0; /* 防止收缩 */
        }

        .switch-status-text.active {
            color: #00d4ff;
        }

        /* 调整参数设置列宽度以适应不同控件 */
        .param-setting {
            width: 130px; /* 增加宽度以容纳开关和状态文本 */
            text-align: center;
            vertical-align: middle;
            padding: 3px;
        }

        /* 新的布局样式：左侧大面板，右侧上下两个小面板 */
        .main-layout {
            display: flex;
            gap: 12px; /* 减少间距 */
            height: calc(100vh - 120px); /* 进一步优化高度计算 */
            max-height: calc(100vh - 120px);
        }

        .left-panel {
            flex: 2;
            display: flex;
            flex-direction: column;
            min-height: 0;
            overflow: hidden;
        }

        .right-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 12px; /* 减少间距 */
            min-height: 0;
            overflow: hidden;
        }

        .control-mode-panel {
            background: rgba(26, 31, 46, 0.95);
            border: 2px solid rgba(0, 212, 255, 0.4);
            border-radius: 8px;
            padding: 10px; /* 减少内边距 */
            overflow: hidden;
            min-width: 0;
            display: flex;
            flex-direction: column;
        }

        /* 控制模式1占用左侧全部空间 */
        .control-mode-panel.mode1 {
            flex: 1;
            min-height: 0;
        }

        /* 控制模式2和3平分右侧空间 */
        .control-mode-panel.mode2,
        .control-mode-panel.mode3 {
            flex: 1;
            min-height: 0;
        }

        /* 表格容器样式 */
        .table-container {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            min-height: 0;
        }

        /* 面板标题优化 */
        .panel-title {
            font-size: 16px;
            color: #00d4ff;
            text-align: center;
            margin-bottom: 8px; /* 减少底部边距 */
            padding: 6px 0; /* 减少内边距 */
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 4px;
            font-weight: bold;
            flex-shrink: 0; /* 防止收缩 */
        }

        /* 表格样式优化 - 支持每行两个参数 */
        .params-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .params-table th {
            background: rgba(0, 212, 255, 0.3);
            color: #00d4ff;
            padding: 6px 3px;
            text-align: center;
            border: 1px solid rgba(0, 212, 255, 0.4);
            font-weight: bold;
            font-size: 12px;
        }

        .params-table td {
            padding: 3px 2px;
            text-align: center;
            border: 1px solid rgba(0, 212, 255, 0.2);
            background: rgba(42, 49, 66, 0.7);
            vertical-align: middle;
            height: 50px; /* 优化行高 */
        }

        .params-table tr:hover td {
            background: rgba(42, 49, 66, 0.9);
            border-color: rgba(0, 212, 255, 0.4);
        }

        /* 序号列样式 */
        .param-index {
            font-size: 12px;
            color: #7a8ba0;
            font-weight: bold;
            width: 30px;
            text-align: center;
        }

        /* 参数名称列样式 */
        .param-name {
            font-size: 12px;
            color: #ffffff;
            text-align: left;
            padding-left: 4px;
            line-height: 1.2;
            max-width: 130px;
            width: 130px;
            word-wrap: break-word;
            overflow: visible;
            white-space: normal;
        }

        /* 当前值列样式 */
        .param-current {
            font-size: 12px;
            color: #00d4ff;
            font-weight: bold;
            width: 70px;
            text-align: center;
        }

        /* 设定值列样式 */
        .param-setting {
            width: 130px;
            text-align: center;
            padding: 0 5px;
        }
    </style>
</head>
<body>
    <!-- MQTT 连接状态指示器 -->
    <div class="mqtt-status-container">
        <div class="mqtt-connection-status disconnected" id="mqtt-status">MQTT 连接中...</div>
        <div class="data-timestamp" id="data-timestamp">等待数据...</div>
    </div>

    <div class="container">
        <div class="header">
            <h1>控制模式</h1>
        </div>

        <div class="main-content">
            <div class="main-layout">
                <!-- 左侧面板：控制模式1 - 仅包含指定的9个参数 -->
                <div class="left-panel">
                    <div class="control-mode-panel mode1">
                        <div class="panel-title">控制模式1</div>
                        <div class="table-container">
                            <table class="params-table">
                                <thead>
                                    <tr>
                                        <th>序号</th>
                                        <th>参数名称</th>
                                        <th>设定值</th>
                                        <th>当前值</th>
                                        <th>序号</th>
                                        <th>参数名称</th>
                                        <th>设定值</th>
                                        <th>当前值</th>
                                    </tr>
                                </thead>
                                <tbody id="control-table-1">
                                    <!-- 指定的9个参数将在这里动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 右侧面板：控制模式2和3 - 留空 -->
                <div class="right-panel">
                    <!-- 控制模式2面板 - 留空 -->
                    <div class="control-mode-panel mode2">
                        <div class="panel-title">控制模式2</div>
                        <div class="table-container">
                            <table class="params-table">
                                <thead>
                                    <tr>
                                        <th>序号</th>
                                        <th>参数名称</th>
                                        <th>设定值</th>
                                        <th>当前值</th>
                                        <th>序号</th>
                                        <th>参数名称</th>
                                        <th>设定值</th>
                                        <th>当前值</th>
                                    </tr>
                                </thead>
                                <tbody id="control-table-2">
                                    <!-- 留空 -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 控制模式3面板 - 留空 -->
                    <div class="control-mode-panel mode3">
                        <div class="panel-title">控制模式3</div>
                        <div class="table-container">
                            <table class="params-table">
                                <thead>
                                    <tr>
                                        <th>序号</th>
                                        <th>参数名称</th>
                                        <th>设定值</th>
                                        <th>当前值</th>
                                        <th>序号</th>
                                        <th>参数名称</th>
                                        <th>设定值</th>
                                        <th>当前值</th>
                                    </tr>
                                </thead>
                                <tbody id="control-table-3">
                                    <!-- 留空 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <button class="send-button" id="send-settings-btn" onclick="handleSendParameterSettings()">下载</button>

        <!-- 状态提示消息 -->
        <div id="status-message" class="status-message"></div>
    </div>

    <!-- 引入通用参数配置脚本 -->
    <script src="../common/parameter-config.js"></script>
    <script>
        // 确保全局变量可访问
        window.parameterManager = null;

        /**
         * 控制模式页面配置 - 精简版
         * 仅包含指定的9个关键参数
         */

        // 指定的9个参数配置
        const controlMode1Group = [
            {
                mqttId: '1914_UL',
                name: '单元级数',
                type: 'integer',
                min: 10,
                max: 12,
                description: 'SVG系统的单元级数，取值范围10-12级'
            },
            {
                mqttId: '1914_PN',
                name: '相数',
                type: 'switch',
                options: ['单相', '三相'],
                description: '系统相数配置：0=单相，1=三相'
            },
            {
                mqttId: '1914_SCM',
                name: 'SVG连接方式',
                type: 'switch',
                options: ['星接', '角接'],
                description: 'SVG连接方式：0=星接，1=角接'
            },
            {
                mqttId: '1914_BCE',
                name: '平衡控制使能',
                type: 'switch',
                options: ['无', '有'],
                description: '平衡控制功能使能：0=无，1=有'
            },
            {
                mqttId: '1914_CSM',
                name: 'CT采样点位置',
                type: 'switch',
                options: ['电网侧', '负载侧'],
                description: '电流互感器采样点位置：0=电网侧，1=负载侧'
            },
            {
                mqttId: '1914_BTM',
                name: '母联状态',
                type: 'switch',
                options: ['不考虑', '考虑'],
                description: '母联状态考虑：0=不考虑，1=考虑'
            },
            {
                mqttId: '1914_TM',
                name: '测试模式',
                type: 'switch',
                options: ['正常模式', '方波测试模式'],
                description: '系统测试模式：0=正常模式，1=方波测试模式'
            },
            {
                mqttId: '1914_CA',
                name: '柜体排列方式',
                type: 'switch',
                options: ['右排列', '左排列'],
                description: '柜体排列方式：0=右排列，1=左排列'
            },
            {
                mqttId: '1914_HCAM',
                name: '谐波补偿类型',
                type: 'switch',
                options: ['自动运行', '手动运行'],
                description: '谐波补偿控制类型：0=自动运行，1=手动运行'
            }
        ];

        // 控制模式2和3留空
        const controlMode2Group = [];
        const controlMode3Group = [];

        // 合并所有参数（仅9个）
        const allControlParameters = [...controlMode1Group];

        // 页面配置对象
        const controlModeConfig = {
            pageTitle: '控制模式',
            panelTitles: ['控制模式1', '控制模式2', '控制模式3'],
            parameters: allControlParameters,
            parametersPerPanel: [9, 0, 0], // 仅控制模式1有9个参数
            parametersPerRow: 1 // 每行显示1个参数（单列布局）
        };

        /**
         * 控制模式专用参数配置管理类
         * 扩展通用参数配置管理器以支持不同类型的控件
         */
        class ControlModeParameterManager extends ParameterConfigManager {
            constructor(config) {
                super(config);
            }

            /**
             * 初始化参数定义（重写以确保正确的序号）
             */
            initializeParams() {
                if (!this.config.parameters || !Array.isArray(this.config.parameters)) {
                    console.error('参数配置无效：parameters 必须是数组');
                    return;
                }

                this.config.parameters.forEach((param, index) => {
                    // 所有参数都属于控制模式1
                    const paramIndexInPanel = index + 1;
                    
                    // 创建参数定义
                    this.paramDefinitions.set(param.mqttId, {
                        ...param,
                        index: index + 1, // 全局序号从1开始
                        panelIndex: 1,    // 都属于控制模式1
                        paramIndexInPanel: paramIndexInPanel // 面板内序号
                    });
                });

                console.log('控制模式参数定义初始化完成，共', this.paramDefinitions.size, '个参数');
            }

            /**
             * 创建整数输入框控件
             * @param {Object} param - 参数配置
             * @param {number} currentValue - 当前值
             * @returns {string} HTML字符串
             */
            createIntegerInput(param, currentValue) {
                const value = currentValue !== undefined ? currentValue : param.min || 0;
                return `
                    <div class="control-container">
                        <input type="number" 
                               class="integer-input" 
                               id="input-${param.mqttId}" 
                               data-mqtt-id="${param.mqttId}"
                               min="${param.min}" 
                               max="${param.max}"
                               value="${value}"
                               title="${param.description || ''}">
                    </div>
                `;
            }

            /**
             * 创建开关控件
             * @param {Object} param - 参数配置
             * @param {number} currentValue - 当前值
             * @returns {string} HTML字符串
             */
            createSwitchControl(param, currentValue) {
                const isActive = currentValue === 1;
                const statusText = isActive ? param.options[1] : param.options[0];
                
                return `
                    <div class="switch-control-container">
                        <div class="toggle-switch ${isActive ? 'active' : ''}" 
                             id="switch-${param.mqttId}" 
                             data-mqtt-id="${param.mqttId}"
                             title="${param.description || ''}">
                        </div>
                        <span class="switch-status-text ${isActive ? 'active' : ''}" id="status-${param.mqttId}">
                            ${statusText}
                        </span>
                    </div>
                `;
            }

            /**
             * 创建参数行HTML
             * @param {Object} param - 参数定义
             * @param {number} currentValue - 当前值
             * @returns {string} 参数行HTML
             */
            createParameterRow(param, currentValue) {
                let settingControl = '';
                
                switch (param.type) {
                    case 'integer':
                        settingControl = this.createIntegerInput(param, currentValue);
                        break;
                    case 'switch':
                        settingControl = this.createSwitchControl(param, currentValue);
                        break;
                    default:
                        settingControl = `<div class="control-container">未知类型</div>`;
                }

                // 确保当前值显示为对应的中文描述
                let displayValue = '';
                if (param.type === 'switch') {
                    displayValue = currentValue === 1 ? param.options[1] : param.options[0];
                } else {
                    displayValue = currentValue !== undefined ? currentValue : '-';
                }

                return `
                    <tr data-mqtt-id="${param.mqttId}">
                        <td class="param-index">${param.index}</td>
                        <td class="param-name" title="${param.description || param.name}">${param.name}</td>
                        <td class="param-setting">${settingControl}</td>
                        <td class="param-current" id="current-${param.mqttId}">${displayValue}</td>
                    </tr>
                `;
            }

            /**
             * 渲染所有参数到对应的面板
             */
            renderAllParameters() {
                console.log('开始渲染控制模式参数...');
                
                // 清空所有表格
                const table1 = document.getElementById('control-table-1');
                const table2 = document.getElementById('control-table-2');
                const table3 = document.getElementById('control-table-3');
                
                if (table1) table1.innerHTML = '';
                if (table2) table2.innerHTML = '';
                if (table3) table3.innerHTML = '';

                // 渲染控制模式1的参数
                const params1 = Array.from(this.paramDefinitions.values())
                    .filter(p => p.panelIndex === 1)
                    .sort((a, b) => a.paramIndexInPanel - b.paramIndexInPanel);

                params1.forEach(param => {
                    const currentValue = this.currentValues.get(param.mqttId);
                    const rowHtml = this.createParameterRow(param, currentValue);
                    if (table1) {
                        table1.insertAdjacentHTML('beforeend', rowHtml);
                    }
                });

                // 为控制模式2和3添加空行提示
                if (table2) {
                    table2.innerHTML = `
                        <tr>
                            <td colspan="8" style="text-align: center; color: #7a8ba0; padding: 20px;">
                                暂无参数配置
                            </td>
                        </tr>
                    `;
                }
                
                if (table3) {
                    table3.innerHTML = `
                        <tr>
                            <td colspan="8" style="text-align: center; color: #7a8ba0; padding: 20px;">
                                暂无参数配置
                            </td>
                        </tr>
                    `;
                }

                // 绑定事件监听器
                this.bindEventListeners();
                console.log('控制模式参数渲染完成');
            }

            /**
             * 绑定事件监听器
             */
            bindEventListeners() {
                // 绑定整数输入框事件
                document.querySelectorAll('.integer-input').forEach(input => {
                    input.addEventListener('change', (e) => {
                        const mqttId = e.target.dataset.mqttId;
                        const value = parseInt(e.target.value);
                        
                        // 验证输入范围
                        const param = this.paramDefinitions.get(mqttId);
                        if (param && param.min !== undefined && param.max !== undefined) {
                            if (value < param.min || value > param.max) {
                                e.target.setCustomValidity(`请输入${param.min}-${param.max}之间的整数`);
                                e.target.reportValidity();
                                return;
                            }
                            e.target.setCustomValidity('');
                        }
                        
                        this.handleParameterChange(mqttId, value);
                    });

                    input.addEventListener('input', (e) => {
                        const mqttId = e.target.dataset.mqttId;
                        const value = parseInt(e.target.value);
                        
                        // 实时验证
                        const param = this.paramDefinitions.get(mqttId);
                        if (param && param.min !== undefined && param.max !== undefined) {
                            if (value >= param.min && value <= param.max) {
                                e.target.style.borderColor = 'rgba(0, 212, 255, 0.3)';
                            } else {
                                e.target.style.borderColor = 'rgba(220, 53, 69, 0.6)';
                            }
                        }
                    });
                });

                // 绑定开关事件
                document.querySelectorAll('.toggle-switch').forEach(switchEl => {
                    switchEl.addEventListener('click', (e) => {
                        const mqttId = e.target.dataset.mqttId;
                        const currentValue = this.currentValues.get(mqttId) || 0;
                        const newValue = currentValue === 1 ? 0 : 1;
                        
                        this.handleParameterChange(mqttId, newValue);
                        
                        // 更新UI
                        e.target.classList.toggle('active', newValue === 1);
                        const statusText = document.getElementById(`status-${mqttId}`);
                        if (statusText) {
                            const param = this.paramDefinitions.get(mqttId);
                            statusText.textContent = newValue === 1 ? param.options[1] : param.options[0];
                            statusText.classList.toggle('active', newValue === 1);
                        }
                    });
                });
            }

            /**
             * 更新参数显示值
             * @param {string} mqttId - MQTT标识符
             * @param {number} value - 新值
             */
            updateParameterDisplay(mqttId, value) {
                const param = this.paramDefinitions.get(mqttId);
                if (!param) return;

                // 更新当前值显示
                const currentValueEl = document.getElementById(`current-${mqttId}`);
                if (currentValueEl) {
                    if (param.type === 'switch') {
                        currentValueEl.textContent = value === 1 ? param.options[1] : param.options[0];
                    } else {
                        currentValueEl.textContent = value;
                    }
                }

                // 更新输入控件
                if (param.type === 'integer') {
                    const inputEl = document.getElementById(`input-${mqttId}`);
                    if (inputEl) {
                        inputEl.value = value;
                    }
                } else if (param.type === 'switch') {
                    const switchEl = document.getElementById(`switch-${mqttId}`);
                    const statusTextEl = document.getElementById(`status-${mqttId}`);
                    
                    if (switchEl) {
                        switchEl.classList.toggle('active', value === 1);
                    }
                    
                    if (statusTextEl) {
                        statusTextEl.textContent = value === 1 ? param.options[1] : param.options[0];
                        statusTextEl.classList.toggle('active', value === 1);
                    }
                }
            }
        }

        /**
         * 页面初始化
         */
        function initializePage() {
            console.log('初始化控制模式页面...');
            
            try {
                // 创建参数管理器实例
                window.parameterManager = new ControlModeParameterManager(controlModeConfig);
                
                // 初始化MQTT连接
                window.parameterManager.initializeMQTT();
                
                // 渲染参数
                window.parameterManager.renderAllParameters();
                
                console.log('控制模式页面初始化完成');
            } catch (error) {
                console.error('页面初始化失败:', error);
                showStatusMessage('页面初始化失败: ' + error.message, 'error');
            }
        }

        /**
         * 处理发送参数设置
         */
        function handleSendParameterSettings() {
            if (window.parameterManager) {
                window.parameterManager.sendParameterSettings();
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializePage);

        // 处理窗口大小变化
        window.addEventListener('resize', () => {
            if (window.parameterManager) {
                // 重新渲染以适应新的窗口大小
                setTimeout(() => {
                    window.parameterManager.renderAllParameters();
                }, 100);
            }
        });
    </script>
</body>
</html>